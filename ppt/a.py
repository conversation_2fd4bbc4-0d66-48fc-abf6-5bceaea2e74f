# 修正段落添加方式
from pptx.util import Inches, Pt
from pptx.dml.color import RGBColor
from pptx import Presentation

# 创建PPT对象
prs = Presentation()
slide_layout = prs.slide_layouts[5]  # 空白布局

# 添加一页幻灯片
slide = prs.slides.add_slide(slide_layout)

# 字体样式设定
title_font_size = Pt(32)
section_title_font_size = Pt(20)
content_font_size = Pt(16)
dark_blue = RGBColor(0, 51, 102)

# 添加标题
title_box = slide.shapes.add_textbox(Inches(0.5), Inches(0.2), Inches(9), Inches(1))
title_frame = title_box.text_frame
title_frame.text = "基于银行核心系统的 Crypto / Stablecoin 创新应用及计划"
title_frame.paragraphs[0].font.size = title_font_size
title_frame.paragraphs[0].font.bold = True
title_frame.paragraphs[0].font.color.rgb = dark_blue

# 添加内容块信息
sections = [
    ("🏦 战略定位", "- 海外注册金融科技公司\n- 提供稳定币融合银行核心系统\n- 服务虚拟银行、Web3平台等\n- 输出 Banking-as-a-Service 能力"),
    ("💡 核心创新", "- 多币种账户（USDC/法币）\n- 稳定币链上清算引擎\n- 链上合规（KYC / Travel Rule）\n- API化核心功能\n- 发薪与消费功能"),
    ("🔧 技术架构", "企业客户/API →\nAPI网关+SDK层 →\n账户系统（多币种账本） →\n稳定币清算引擎（链上+CEX） →\n链上合规系统"),
    ("📈 商业模式", "客户群：虚拟银行/Web3/平台\n收入：API费/交易手续费\n合作：Circle/Fireblocks/受监管银行\n监管：新加坡MAS、ADGM、香港VASP")
]

positions = [
    (Inches(0.5), Inches(1.2)),
    (Inches(5.2), Inches(1.2)),
    (Inches(0.5), Inches(3.2)),
    (Inches(5.2), Inches(3.2))
]

for (title, content), (left, top) in zip(sections, positions):
    box = slide.shapes.add_textbox(left, top, Inches(4.2), Inches(1.8))
    frame = box.text_frame
    p = frame.paragraphs[0]
    p.text = title
    p.font.size = section_title_font_size
    p.font.bold = True
    p.font.color.rgb = dark_blue
    p.space_after = Pt(4)
    for line in content.split('\n'):
        para = frame.add_paragraph()
        para.text = line
        para.font.size = content_font_size

# 添加页脚
footer_box = slide.shapes.add_textbox(Inches(0.5), Inches(5.3), Inches(9), Inches(0.4))
footer_frame = footer_box.text_frame
footer_frame.text = "✅ 下一步：MVP搭建 → 合规对接 → 市场拓展"
footer_frame.paragraphs[0].font.size = Pt(14)
footer_frame.paragraphs[0].font.color.rgb = RGBColor(100, 100, 100)

# 保存PPT
pptx_path = "/mnt/data/Crypto_Stablecoin_Banking_Innovation.pptx"
prs.save(pptx_path)

pptx_path