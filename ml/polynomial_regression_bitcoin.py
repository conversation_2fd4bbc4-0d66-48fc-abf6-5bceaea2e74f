import pandas as pd
import numpy as np
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import PolynomialFeatures
from sklearn.linear_model import LinearRegression
from sklearn.pipeline import make_pipeline
from sklearn.metrics import mean_absolute_error, mean_squared_error, r2_score
import matplotlib.pyplot as plt
import matplotlib
matplotlib.use('Agg')  # 使用非交互式后端，避免显示窗口

# 加载数据
df = pd.read_csv("./dataset/challenge-2-bitcoin.csv", header=0)
print("数据集概览:")
print(df.head())

data = df[['Date','btc_market_price','btc_total_bitcoins', 'btc_transaction_fees']]
print(data.head())

fig, axes = plt.subplots(1, 3, figsize=(16, 5))

axes[0].plot(data['btc_market_price'], 'green')
axes[0].set_xlabel('time')
axes[0].set_ylabel('btc_market_price')

axes[1].plot(data['btc_total_bitcoins'], 'blue')
axes[1].set_xlabel('time')
axes[1].set_ylabel('btc_total_bitcoins')

axes[2].plot(data['btc_transaction_fees'], 'brown')
axes[2].set_xlabel('time')
axes[2].set_ylabel('btc_transaction_fees')

plt.savefig('bitcoin_data.png')
plt.show()

# # 准备数据
X_train, X_test, y_train,  y_test = train_test_split(data[['btc_total_bitcoins', 'btc_transaction_fees']], data[['btc_market_price']], test_size=0.3, random_state=42)
print(len(X_train), len(X_test), len(y_train), len(y_test), X_train.shape, X_test.shape, y_train.shape, y_test.shape)

# 创建多项式回归模型
# 尝试不同的多项式次数
# degrees = [1, 2, 3, 4, 5]
degree = 1
N =10
models = {}
predictions = {}
mae_scores = {}
mse_scores = {}
# r2_scores = {}

plt.figure(figsize=(14, 10))

# 训练不同次数的多项式回归模型
while degree <= N:
    # 创建多项式回归模型
    model = make_pipeline(
        PolynomialFeatures(degree=degree, include_bias=False),
        LinearRegression()
    )

    # 训练模型
    model.fit(X_train, y_train)

    # 保存模型
    models[degree] = model

    # 预测
    y_pred = model.predict(X_test)
    predictions[degree] = y_pred

    # 计算评估指标
    mae_scores[degree] = mean_absolute_error(y_test, y_pred.flatten())
    mse_scores[degree] = mean_squared_error(y_test, y_pred)
    # r2 = r2_score(y, y_pred)
    # r2_scores[degree] = r2

    print(f"\n多项式次数: {degree}")
    print(f"平均绝对误差 (MAE): {mae_scores[degree]:.2f}")
    print(f"均方误差 (MSE): {mse_scores[degree]:.2f}")
    # print(f"决定系数 (R²): {r2:.4f}")

    degree = degree +1

    # # 绘制拟合曲线
    # plt.subplot(2, 3, degree)
    # plt.scatter(X, y, color='blue', label='Actual Data')

    # # 为了绘制平滑曲线，创建更多的点
    # X_smooth = np.linspace(X.min(), X.max(), 100).reshape(-1, 1)
    # y_smooth = model.predict(X_smooth)

    # plt.plot(X_smooth, y_smooth, color='red', label=f'Polynomial Fit (degree={degree})')
    # plt.title(f'Polynomial Regression (degree={degree})\nMSE={mse:.2f}, R²={r2:.4f}')
    # plt.xlabel('Year')
    # plt.ylabel('Value')
    # plt.legend()
    # plt.grid(True)

# # 找出最佳模型
best_degree = min(mae_scores, key=mae_scores.get)
print(f"\n基于MAE的最佳多项式次数: {best_degree}")
print(f"最佳MAE: {mae_scores[best_degree]:.2f}")


print(mae_scores[:10:3])
# print(f"最佳MSE: {mse_scores[best_degree]:.2f}")
# print(f"最佳R²: {r2_scores[best_degree]:.4f}")

# # 绘制所有模型的比较
# plt.subplot(2, 3, 6)
# plt.scatter(X, y, color='blue', label='实际数据')

# for degree in degrees:
#     X_smooth = np.linspace(X.min(), X.max(), 100).reshape(-1, 1)
#     y_smooth = models[degree].predict(X_smooth)
#     plt.plot(X_smooth, y_smooth, label=f'degree={degree}')

# plt.title('Comparison of Different Polynomial Degrees')
# plt.xlabel('Year')
# plt.ylabel('Value')
# plt.legend()
# plt.grid(True)

# plt.tight_layout()
# plt.savefig('polynomial_regression_results.png')
# plt.show()

# # 使用最佳模型进行预测
# best_model = models[best_degree]

# # 预测未来5年
# future_years = np.array(range(2017, 2022)).reshape(-1, 1)
# future_predictions = best_model.predict(future_years)

# print("\n未来5年预测:")
# for year, prediction in zip(future_years.flatten(), future_predictions):
#     print(f"{year}: {prediction:.2f}")

# # 绘制包含预测的图表
# plt.figure(figsize=(10, 6))
# plt.scatter(X, y, color='blue', label='Historical Data')

# # 绘制历史数据的拟合曲线
# X_smooth = np.linspace(X.min(), X.max(), 100).reshape(-1, 1)
# y_smooth = best_model.predict(X_smooth)
# plt.plot(X_smooth, y_smooth, color='red', label=f'Historical Data Fit (degree={best_degree})')

# # 绘制预测
# plt.scatter(future_years, future_predictions, color='green', label='Predictions')
# plt.plot(future_years, future_predictions, 'g--')

# plt.title(f'Best Polynomial Model (degree={best_degree}) and Future Predictions')
# plt.xlabel('Year')
# plt.ylabel('Value')
# plt.legend()
# plt.grid(True)
# plt.savefig('polynomial_regression_prediction.png')
# plt.show()

# # 输出模型系数
# if best_degree == 1:
#     print("\n线性回归模型:")
#     linear_model = best_model.named_steps['linearregression']
#     print(f"斜率: {linear_model.coef_[0]:.4f}")
#     print(f"截距: {linear_model.intercept_:.4f}")
#     print(f"方程: y = {linear_model.coef_[0]:.4f} * x + {linear_model.intercept_:.4f}")
# else:
#     print(f"\n多项式回归模型 (次数={best_degree}):")
#     poly_model = best_model.named_steps['linearregression']
#     print("系数:", poly_model.coef_)
#     print("截距:", poly_model.intercept_)
