import pandas as pd

### 代码开始 ### (≈ 2 行代码)
df = pd.read_csv("./dataset/challenge-1-beijing.csv")
df.head()  # 不需要 print()，IPython 会自动显示结果
### 代码结束 ###


### 代码开始 ### (≈ 1 行代码)
features = df[df.columns.drop(['小区名字','房型','每平米价格'])]
target = df['每平米价格']
### 代码结束 ###

pd.concat([features, target], axis=1).head()

### 代码开始 ### (≈ 4 行代码)
# 计算70%的索引位置
split_idx = int(len(features) * 0.7)

# 使用Pandas切片方法划分数据集
train_features = features.iloc[:split_idx]
test_features = features.iloc[split_idx:]
train_target = target.iloc[:split_idx]
test_target = target.iloc[split_idx:]

# 打印数据集大小以验证划分结果
print(f"训练集大小: {len(train_features)}")
print(f"测试集大小: {len(test_features)}")
print(f"训练集比例: {len(train_features) / len(features):.2f}")
### 代码结束 ###