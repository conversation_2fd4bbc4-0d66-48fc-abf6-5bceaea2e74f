import pandas as pd
from sklearn.linear_model import LinearRegression
from sklearn.model_selection import train_test_split
from sklearn.ensemble import RandomForestRegressor
from sklearn.metrics import mean_absolute_error, r2_score, mean_squared_error

import matplotlib.pyplot as plt

# 加载数据
df = pd.read_csv("./dataset/challenge-1-beijing.csv")

# 特征工程
features = df[df.columns.drop(['小区名字','房型','每平米价格'])]
target = df['每平米价格']

# 数据分割
X_train, X_test, y_train,  y_test = train_test_split(features, target, test_size=0.3, random_state=42)

# 模型训练
## 随机森林？
model = RandomForestRegressor(n_estimators=100, random_state=42)
model.fit(X_train, y_train)
## 最小二乘法
# model = LinearRegression()  # 建立模型
# model.fit(X_train, y_train)  # 训练模型
# model.coef_, model.intercept_  # 输出训练后的模型参数和截距项
# print(f"coef_:", model.coef_)
# print(f"intercept_:", model.intercept_)

# 预测
y_pred = model.predict(X_test)

# 评估
mae = mean_absolute_error(y_test, y_pred)
r2 = r2_score(y_test, y_pred)
print(f"平均绝对误差: {mae:.2f}")
print(f"R2分数: {r2:.2f}")
# mse = mean_squared_error(y_test, y_pred)
# print(f"均方误差: {mse:.2f}")

# 特征重要性
importances = model.feature_importances_
plt.bar(features.columns, importances)
plt.xticks(rotation=45)
plt.title('特征重要性')
plt.rcParams['font.sans-serif'] = ['Arial Unicode MS']  # 或其他支持中文的字体
plt.rcParams['axes.unicode_minus'] = False
plt.show()
