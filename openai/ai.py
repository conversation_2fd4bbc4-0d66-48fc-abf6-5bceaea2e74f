from openai import OpenAI

client = OpenAI(
    # This is the default and can be omitted
    api_key="***************************************************",
)

chat_completion = client.chat.completions.create(
    messages=[
        {
            "role": "user",
            # "content": "What is this pdf talking about? I have upload the pdf to chatgpt, and the docId is file-tTucqQr0LiTnD5OT9FT6mPVn",
            "content": "What is this pdf talking about? the content is: 29th Floor,\nOne International Finance Centre,\n1 Harbour View Street,\nCentral, HK\nTel: (852) 28722000\nFax: (852) 28722102\n19  Dec 2023\nTrade Confirmation\nPage\n2\nof\n2\nCE No.:AEN894\nEXCHANGE PARTICIPANT OF THE STOCK EXCHANGE OF HONG KONG LTD.\nCHINA INTERNATIONAL CAPITAL CORPORATION HONG KONG SECURITIES LIMITED \n中國國際金融香港證券有限公司\nAttn:\nTo:\nMERIT ASSET MANAGEMENT LIMITED\nMERIT ASSET MANAGEMENT LIMITED\nFrom:\nCICC HK SEC.LTD.\nTRADE DETAILS :\nCODE:\nNAME:\nWE HAVING ACTED AS PRINCIPAL.\nMERIT ASSET MANAGEMENT LIMITED\nListed stock traded on\n19 Dec 2023\nTrade No\nSettle Date\nInstrument Code and Name\nB/S\nQuantity\nUnit Price\nCur\nConsideration\nTransaction Costs\nSett.Amount\nBUY\n20 Dec 2023\nAccrued Int. :\n 0.00\nOther Fee :\n 0.00\nBrokerage:\nUSD\n 0.00\n232345277\n 0.00\nSettlement Instruction :\nYOUR PORTFOLIO WILL BE UPDATED ON SETTLEMENT DATE.\nANY COSTS ARISING FROM SETTLEMENT FAILURE DUE TO YOUR\nACCORD WILL BE PASSED TO YOU ACCORDINGLY.\nPLEASE NOTIFY US WITHIN 24 HRS IF THERE IS ANY\nDISCREPANCY. OTHERWISE WE ASSUME YOUR FULL\nAGREEMENT\nONLY INSTITUTIONAL PROFESSIONAL INVESTORS ARE ALLOWED TO TRADE STAR SHARES AND CHINEXT SHARES THROUGH CHINA CONNECT SERVICE OF THE HKEX\n. \nPLEASE CONTACT YOUR SALES WITHIN 2 BUSINESS DAYS UPON THE RECEIPT OF THIS TRADE CONFIRMATION IF YOUR FIRM IS AWARE OF ANY TRANSACTIONS MADE FOR \nOR ON BEHALF OF YOUR UNDERLYING CLIENT WHO IS NOT INSTITUTIONAL PROFESSIONAL INVESTOR.\nTHANKS & REGARDS\nSETTLEMENT DEPT.\nCICCHKS"
        }
    ],
    # model="gpt-3.5-turbo",
    model="gpt-4-turbo",
)
print(chat_completion)

# file = client.files.retrieve("file-23yUqMpJ7Mj8K0uMA22OsW77")
# print(file)


# file = client.files.create(
#   file=open("../pdf/doc.pdf", "rb"),
#   purpose="assistants"
# )
# print(file)


# docs = client.files.list()
# print(docs)

