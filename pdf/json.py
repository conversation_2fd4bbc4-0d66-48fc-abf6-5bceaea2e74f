import csv

# New JSON data provided by the user (shortened for representation)
api_data_new = [
    {
        "id": 389673,
        "desc": "created by batch script1",
        "path": "/v1/customer/customer-check",
        "event": "WLCU000001"
    },
    {
        "id": 392238,
        "desc": "WL200013",
        "path": "/WL200013",
        "event": "WL200013"
    },
    {
        "id": 389683,
        "desc": "created by batch script",
        "path": "/mock",
        "event": "OB3O0001"
    },
    {
        "id": 389682,
        "desc": "created by batch script",
        "path": "/v1/nfps/merchant/qr-query",
        "event": "WL200071"
    },
    {
        "id": 389678,
        "desc": "created by batch script",
        "path": "/v1/isaving/agreement/create",
        "event": "WLSV000081"
    },
    {
        "id": 389677,
        "desc": "created by batch script",
        "path": "/v1/isaving/account-balance-list/query",
        "event": "WLSV100102"
    },
    {
        "id": 389672,
        "desc": "created by batch script",
        "path": "/sms/json",
        "event": "NX000001"
    },
    {
        "id": 389669,
        "desc": "created by batch script",
        "path": "/resources/accessTokens",
        "event": "SS000001"
    },
    {
        "id": 389665,
        "desc": "created by batch script",
        "path": "/cards",
        "event": "RE000001"
    },
    {
        "id": 389656,
        "desc": "created by batch script",
        "path": "/wallet/request/activate/verify",
        "event": "DC000001"
    },
    {
        "id": 389655,
        "desc": "created by batch script",
        "path": "/v1/bnpl/order",
        "event": "BP000031"
    },
    {
        "id": 389654,
        "desc": "created by batch script",
        "path": "/v1/bnpl/update/order/status",
        "event": "BP000018"
    },
    {
        "id": 389652,
        "desc": "created by batch script",
        "path": "/v1/crm/consent/getconsentbytype",
        "event": "CS100028"
    },
    {
        "id": 389651,
        "desc": "created by batch script",
        "path": "/open-banking/products-services/v1/business-financings",
        "event": "OB3O0016"
    },
    {
        "id": 389647,
        "desc": "created by batch script",
        "path": "/v1/wallet/create-isaving-account-customer",
        "event": "SV500004"
    },
    {
        "id": 389646,
        "desc": "created by batch script",
        "path": "/v1/wallet/force/signout",
        "event": "WL500068"
    },
    {
        "id": 389640,
        "desc": "created by batch script",
        "path": "/v1/wallet/kyc/review-status/retrieve",
        "event": "WL500058"
    },
    {
        "id": 389639,
        "desc": "created by batch script",
        "path": "/v1/wallet/transaction/verification",
        "event": "WL500057"
    },
    {
        "id": 389638,
        "desc": "created by batch script",
        "path": "/v1/wallet/kyc/access-token/query",
        "event": "WL500069"
    },
    {
        "id": 389637,
        "desc": "created by batch script",
        "path": "/v1/wallet/sms/otp/verify",
        "event": "WL500061"
    },
    {
        "id": 389636,
        "desc": "created by batch script",
        "path": "/v1/wallet/sms/otp/send",
        "event": "WL500060"
    },
    {
        "id": 389635,
        "desc": "created by batch script",
        "path": "/v1/wallet/point/membership/query",
        "event": "WL500054"
    },
    {
        "id": 389634,
        "desc": "created by batch script",
        "path": "/v1/wallet/point/exchange/rate",
        "event": "WL500053"
    },
    {
        "id": 389633,
        "desc": "created by batch script",
        "path": "/v1/wallet/point/membership/add",
        "event": "WL500052"
    },
    {
        "id": 389632,
        "desc": "created by batch script",
        "path": "/v1/wallet/point/transfer/confirmation",
        "event": "WL500051"
    },
    {
        "id": 389631,
        "desc": "created by batch script",
        "path": "/v1/wallet/point-payment/query",
        "event": "WL500050"
    },
    {
        "id": 389630,
        "desc": "created by batch script",
        "path": "/v1/wallet/consent/save-state",
        "event": "WL500049"
    },
    {
        "id": 389629,
        "desc": "created by batch script",
        "path": "/v1/payment/order/ref/request",
        "event": "WL500047"
    },
    {
        "id": 389628,
        "desc": "created by batch script",
        "path": "/v1/wallet/query/bank-account/currency",
        "event": "WL500046"
    },
    {
        "id": 389627,
        "desc": "created by batch script",
        "path": "/v1/wallet/query/account/balance/detail",
        "event": "WL500042"
    },
    {
        "id": 389626,
        "desc": "created by batch script",
        "path": "/v1/wallet/card/list",
        "event": "WL500067"
    },
    {
        "id": 389625,
        "desc": "created by batch script",
        "path": "/v1/test/card/list",
        "event": "WL100007"
    },
    {
        "id": 389624,
        "desc": "created by batch script",
        "path": "/v1/db/card/list",
        "event": "WL900015"
    },
    {
        "id": 389623,
        "desc": "created by batch script",
        "path": "/v1/wallet/user-consent/save-state",
        "event": "WL000032"
    },
    {
        "id": 389622,
        "desc": "created by batch script",
        "path": "/v1/wallet/scan-to-pay/retrieve",
        "event": "WL000030"
    },
    {
        "id": 389621,
        "desc": "created by batch script",
        "path": "/v1/wallet/BOS/query/account/balance/detail",
        "event": "WL000026"
    },
    {
        "id": 389620,
        "desc": "created by batch script",
        "path": "/v1/isaving/BOS/create/main/agreement",
        "event": "SV000081"
    },
    {
        "id": 389619,
        "desc": "created by batch script",
        "path": "/v1/isaving/agreement/freeze",
        "event": "SV000075"
    },
    {
        "id": 389618,
        "desc": "created by batch script",
        "path": "/v1/isaving/agreement/balance/list/query",
        "event": "SV000080"
    },
    {
        "id": 389617,
        "desc": "created by batch script",
        "path": "/v1/saving/get-check/agreement/account",
        "event": "SV100103"
    },
    {
        "id": 389616,
        "desc": "created by batch script",
        "path": "/v1/wallet/common/pin/limit",
        "event": "WL500036"
    },
    {
        "id": 389615,
        "desc": "created by batch script",
        "path": "/v1/wallet/common/get-option-info",
        "event": "WL500029"
    },
    {
        "id": 389614,
        "desc": "created by batch script",
        "path": "/v1/wallet/topup/confirm",
        "event": "WL500027"
    },
    {
        "id": 389613,
        "desc": "created by batch script",
        "path": "/v1/wallet/transfer/confirm",
        "event": "WL500025"
    },
    {
        "id": 389612,
        "desc": "created by batch script",
        "path": "/v1/wallet/account/verify",
        "event": "WL500024"
    },
    {
        "id": 389611,
        "desc": "created by batch script",
        "path": "/v1/wallet/payment/linked-bank-account/update",
        "event": "WL500022"
    },
    {
        "id": 389610,
        "desc": "created by batch script",
        "path": "/v1/wallet/payment/sof/list",
        "event": "WL500021"
    },
    {
        "id": 389609,
        "desc": "created by batch script",
        "path": "/v1/wallet/open/sub/account",
        "event": "WL500020"
    },
    {
        "id": 389608,
        "desc": "created by batch script",
        "path": "/v1/wallet/add/payment/card",
        "event": "WL500018"
    },
    {
        "id": 389607,
        "desc": "created by batch script",
        "path": "/v1/wallet/verify/pin",
        "event": "WL500017"
    },
    {
        "id": 389606,
        "desc": "created by batch script",
        "path": "/v1/wallet/user-customer-info/edit",
        "event": "WL500016"
    },
    {
        "id": 389605,
        "desc": "created by batch script",
        "path": "/v1/wallet/user-customer-info/query",
        "event": "WL500010"
    },
    {
        "id": 389604,
        "desc": "created by batch script",
        "path": "/v1/wallet/pin/reset",
        "event": "WL500008"
    },
    {
        "id": 389603,
        "desc": "created by batch script",
        "path": "/v1/wallet/public-key/query",
        "event": "WL500007"
    },
    {
        "id": 389602,
        "desc": "created by batch script",
        "path": "/v1/wallet/email-code/verify",
        "event": "WL500006"
    },
    {
        "id": 389601,
        "desc": "created by batch script",
        "path": "/v1/wallet/account-balance-list/query",
        "event": "WL500004"
    },
    {
        "id": 389600,
        "desc": "created by batch script",
        "path": "/v1/wallet/BOS/verify/random/code",
        "event": "WL000019"
    },
    {
        "id": 389599,
        "desc": "created by batch script",
        "path": "/v1/wallet/BOS/generate/random/code",
        "event": "WL000018"
    },
    {
        "id": 389598,
        "desc": "created by batch script",
        "path": "/v1/wallet/BOS/query/wallet/account/list",
        "event": "WL000020"
    },
    {
        "id": 389597,
        "desc": "created by batch script",
        "path": "/v1/wallet/user/login",
        "event": "WL500003"
    },
    {
        "id": 389596,
        "desc": "created by batch script",
        "path": "/v1/wallet/login/request",
        "event": "WL500002"
    },
    {
        "id": 389595,
        "desc": "created by batch script",
        "path": "/v1/wallet/signup",
        "event": "WL500001"
    },
    {
        "id": 389594,
        "desc": "created by batch script",
        "path": "/v1/wallet/payment/confirmation",
        "event": "WL5PMCF0"
    },
    {
        "id": 389593,
        "desc": "created by batch script",
        "path": "/v1/customer/BOS/query/signature/consent/info",
        "event": "CU000128"
    },
    {
        "id": 389592,
        "desc": "created by batch script",
        "path": "/v1/customer/BOS/verify/kyc",
        "event": "CU000121"
    },
    {
        "id": 389591,
        "desc": "created by batch script",
        "path": "/v1/wallet/BOS/query/wallet/account/balance/list",
        "event": "WL000014"
    },
    {
        "id": 389590,
        "desc": "created by batch script",
        "path": "/v1/wallet/BOS/user/register",
        "event": "WL000013"
    },
    {
        "id": 389589,
        "desc": "created by batch script",
        "path": "/v1/wallet/BOS/open/saving/account",
        "event": "WL000011"
    },
    {
        "id": 389588,
        "desc": "created by batch script",
        "path": "/v1/wallet/BOS/query/wallet/product",
        "event": "WL000010"
    },
    {
        "id": 389587,
        "desc": "created by batch script",
        "path": "/v1/prod/BOS/query/wallet/product",
        "event": "PD000131"
    },
    {
        "id": 389586,
        "desc": "created by batch script",
        "path": "/v1/customer/BOS/create/signature/consent/info",
        "event": "CU000125"
    },
    {
        "id": 389585,
        "desc": "created by batch script",
        "path": "/v1/wallet/user-wallet-list/query",
        "event": "WL000022"
    },
    {
        "id": 389584,
        "desc": "created by batch script",
        "path": "/v1/wallet/query/limit/param",
        "event": "CM0LMT02"
    },
    {
        "id": 389583,
        "desc": "created by batch script",
        "path": "/v1/wallet/query/pin-limit-rule",
        "event": "WL0LMT04"
    },
    {
        "id": 389582,
        "desc": "created by batch script",
        "path": "/v1/wallet/check/user",
        "event": "WL0CTPQ1"
    },
    {
        "id": 389581,
        "desc": "created by batch script",
        "path": "/v1/wallet/account/binding-external",
        "event": "WL0BEPC0"
    },
    {
        "id": 389580,
        "desc": "created by batch script",
        "path": "/v1/wallet/query/bindcard/list",
        "event": "WL0QCARD"
    },
    {
        "id": 389579,
        "desc": "created by batch script",
        "path": "/v1/wallet/get-transaction-list",
        "event": "WL0TXN02"
    },
    {
        "id": 389578,
        "desc": "created by batch script",
        "path": "/v1/wallet/external-card/remove",
        "event": "WL500028"
    },
    {
        "id": 389577,
        "desc": "created by batch script",
        "path": "/v1/wallet/account/binding-account",
        "event": "WL0ACC01"
    },
    {
        "id": 389576,
        "desc": "created by batch script",
        "path": "/v1/wallet/get-transaction-detail",
        "event": "WL0TXN01"
    },
    {
        "id": 389575,
        "desc": "created by batch script",
        "path": "/v1/wallet/query/wallet/balance",
        "event": "WL000005"
    },
    {
        "id": 389574,
        "desc": "created by batch script",
        "path": "/v1/wallet/consent/inter-wallet/retrieve",
        "event": "WL500040"
    },
    {
        "id": 389573,
        "desc": "created by batch script",
        "path": "/v1/wallet/get-consent",
        "event": "WL0CSN01"
    },
    {
        "id": 389572,
        "desc": "created by batch script",
        "path": "/v1/wallet/limitsetting",
        "event": "WL0LMT03"
    },
    {
        "id": 389571,
        "desc": "created by batch script",
        "path": "/v1/wallet/wallet-limit/update",
        "event": "WL500013"
    },
    {
        "id": 389570,
        "desc": "created by batch script",
        "path": "/v1/wallet/customer-detail/query",
        "event": "WL500012"
    },
    {
        "id": 389569,
        "desc": "created by batch script",
        "path": "/v1/wallet/query/wallet/limit",
        "event": "WL500011"
    },
    {
        "id": 389568,
        "desc": "created by batch script",
        "path": "/v1/wallet/forgot/setpin",
        "event": "WL0PIN04"
    },
    {
        "id": 389567,
        "desc": "created by batch script",
        "path": "/v1/wallet/forgotpin/verify",
        "event": "WL0PIN03"
    },
    {
        "id": 389566,
        "desc": "created by batch script",
        "path": "/v1/wallet/forgotpin/send",
        "event": "WL0PIN02"
    },
    {
        "id": 389565,
        "desc": "created by batch script",
        "path": "/v1/wallet/get-tranding-account-list",
        "event": "WL0ACC02"
    },
    {
        "id": 389564,
        "desc": "created by batch script",
        "path": "/v1/wallet/verify-forgot-pin-authentication-code",
        "event": "WL0PIN03"
    },
    {
        "id": 389563,
        "desc": "created by batch script",
        "path": "/v1/wallet/p2p/transfer/macro",
        "event": "WLMP2P03"
    },
    {
        "id": 389562,
        "desc": "created by batch script",
        "path": "/v1/wallet/p2p/transfer",
        "event": "WL0P2P03"
    },
    {
        "id": 389561,
        "desc": "created by batch script",
        "path": "/v1/wallet/aotuid/verification",
        "event": "WL0P2P02"
    },
    {
        "id": 389560,
        "desc": "created by batch script",
        "path": "/v1/wallet/amount/verification",
        "event": "WL0P2P01"
    },
    {
        "id": 389559,
        "desc": "created by batch script",
        "path": "/v1/wallet/signoff",
        "event": "WL500015"
    },
    {
        "id": 389558,
        "desc": "created by batch script",
        "path": "/v1/wallet/send-forgot-pin-email",
        "event": "WL500019"
    },
    {
        "id": 389557,
        "desc": "created by batch script",
        "path": "/v1/wallet/pin/verify",
        "event": "WL0PIN05"
    },
    {
        "id": 389556,
        "desc": "created by batch script",
        "path": "/v1/wallet/change-pin",
        "event": "WL0PIN05"
    },
    {
        "id": 389555,
        "desc": "created by batch script",
        "path": "/v1/wallet/reset-pin",
        "event": "WL0PIN04"
    },
    {
        "id": 389554,
        "desc": "created by batch script",
        "path": "/v1/get/now/time",
        "event": "FP000118"
    },
    {
        "id": 389553,
        "desc": "created by batch script",
        "path": "/v1/customer/edit-history/query",
        "event": "CU000063"
    },
    {
        "id": 389552,
        "desc": "created by batch script",
        "path": "/v1/customer/apply-new-customer",
        "event": "CU000035"
    },
    {
        "id": 389551,
        "desc": "created by batch script",
        "path": "/v1/customer/approve-new-customer",
        "event": "CU000040"
    },
    {
        "id": 389550,
        "desc": "created by batch script",
        "path": "/v1/customer/modify-customer/apply",
        "event": "CU000042"
    },
    {
        "id": 389549,
        "desc": "created by batch script",
        "path": "/v1/customer/modify-customer/approve",
        "event": "CU000054"
    },
    {
        "id": 389548,
        "desc": "created by batch script",
        "path": "/v1/customer/customer-pdf",
        "event": "CU000059"
    },
    {
        "id": 389547,
        "desc": "created by batch script",
        "path": "/v1/isaving/unfreeze",
        "event": "SV000076"
    },
    {
        "id": 389546,
        "desc": "created by batch script",
        "path": "/v1/isaving/reverse-transaction",
        "event": "SV000057"
    },
    {
        "id": 389545,
        "desc": "created by batch script",
        "path": "/v1/isaving/reversal-transaction/detail",
        "event": "SV000058"
    },
    {
        "id": 389544,
        "desc": "created by batch script",
        "path": "/v1/isaving/agreement/opensubaccount",
        "event": "SV000065"
    },
    {
        "id": 389543,
        "desc": "created by batch script",
        "path": "/v1/isaving/agreement/foreign-currency-transfer",
        "event": "SV000064"
    },
    {
        "id": 389542,
        "desc": "created by batch script",
        "path": "/v1/isaving/main-agreement/continued-freezing",
        "event": "SV000063"
    },
    {
        "id": 389541,
        "desc": "created by batch script",
        "path": "/v1/isaving/main-agreement/unfreeze",
        "event": "SV000062"
    },
    {
        "id": 389540,
        "desc": "created by batch script",
        "path": "/v1/isaving/main-agreement/frozen",
        "event": "SV000061"
    },
    {
        "id": 389539,
        "desc": "created by batch script",
        "path": "/v1/isaving/exchange-rate/trial",
        "event": "SV000059"
    },
    {
        "id": 389538,
        "desc": "created by batch script",
        "path": "/v1/isaving/SV000056",
        "event": "SV000056"
    },
    {
        "id": 389537,
        "desc": "created by batch script",
        "path": "/v1/isaving/main-agreement/detail",
        "event": "SV000051"
    },
    {
        "id": 389536,
        "desc": "created by batch script",
        "path": "/v1/isaving/main-agreement/list",
        "event": "SV000050"
    },
    {
        "id": 389535,
        "desc": "created by batch script",
        "path": "/v1/isaving/main-agreement/close",
        "event": "SV000060"
    },
    {
        "id": 389534,
        "desc": "created by batch script",
        "path": "/v1/isaving/customer/update",
        "event": "SV000069"
    },
    {
        "id": 389533,
        "desc": "created by batch script",
        "path": "/v1/isaving/customer/inquiry",
        "event": "SV000068"
    },
    {
        "id": 389532,
        "desc": "created by batch script",
        "path": "/v1/pricing/fee/query-inactive-fee-type",
        "event": "PF000029"
    },
    {
        "id": 389531,
        "desc": "created by batch script",
        "path": "/v1/pricing/fee/query-fee-type",
        "event": "PF000027"
    },
    {
        "id": 389530,
        "desc": "created by batch script",
        "path": "/v1/pricing/fee/delete-fee-type",
        "event": "PF000022"
    },
    {
        "id": 389529,
        "desc": "created by batch script",
        "path": "/v1/pricing/fee/create-fee-type",
        "event": "PF000007"
    },
    {
        "id": 389528,
        "desc": "created by batch script",
        "path": "/v1/common/first-repayment-date-calc",
        "event": "CM000055"
    },
    {
        "id": 389527,
        "desc": "created by batch script",
        "path": "/v1/isaving/key",
        "event": "SV000055"
    },
    {
        "id": 389526,
        "desc": "created by batch script",
        "path": "/v1/isaving/customer/logout",
        "event": "CU000071"
    },
    {
        "id": 389525,
        "desc": "created by batch script",
        "path": "/v1/isaving/customer/login",
        "event": "CU000065"
    },
    {
        "id": 389524,
        "desc": "created by batch script",
        "path": "/v1/product/fee/base/info",
        "event": "PD000114"
    },
    {
        "id": 389523,
        "desc": "created by batch script",
        "path": "/v1/common/key-maintain",
        "event": "CM000044"
    },
    {
        "id": 389522,
        "desc": "created by batch script",
        "path": "/v1/common/random-code-delete",
        "event": "CM000041"
    },
    {
        "id": 389521,
        "desc": "created by batch script",
        "path": "/v1/common/random-code-generate",
        "event": "CM000040"
    },
    {
        "id": 389520,
        "desc": "created by batch script",
        "path": "/v1/banking-unc/message/send",
        "event": "NT000013"
    },
    {
        "id": 389519,
        "desc": "created by batch script",
        "path": "/v1/banking-unc/target/update",
        "event": "NT000012"
    },
    {
        "id": 389518,
        "desc": "created by batch script",
        "path": "/v1/banking-unc/target/add",
        "event": "NT000011"
    },
    {
        "id": 389517,
        "desc": "created by batch script",
        "path": "/v1/banking-unc/strategy/delete",
        "event": "NT000010"
    },
    {
        "id": 389516,
        "desc": "created by batch script",
        "path": "/v1/banking-unc/strategy/update",
        "event": "NT000009"
    },
    {
        "id": 389515,
        "desc": "created by batch script",
        "path": "/v1/banking-unc/strategy/query-detail",
        "event": "NT000008"
    },
    {
        "id": 389514,
        "desc": "created by batch script",
        "path": "/v1/banking-unc/strategy/query-list",
        "event": "NT000007"
    },
    {
        "id": 389513,
        "desc": "created by batch script",
        "path": "/v1/banking-unc/strategy/create",
        "event": "NT000007"
    },
    {
        "id": 389512,
        "desc": "created by batch script",
        "path": "/v1/banking-unc/template/delete",
        "event": "NT000005"
    },
    {
        "id": 389511,
        "desc": "created by batch script",
        "path": "/v1/banking-unc/template/update",
        "event": "NT000004"
    },
    {
        "id": 389510,
        "desc": "created by batch script",
        "path": "/v1/banking-unc/template/query-detail",
        "event": "NT000003"
    },
    {
        "id": 389509,
        "desc": "created by batch script",
        "path": "/v1/banking-unc/template/query-list",
        "event": "NT000002"
    },
    {
        "id": 389508,
        "desc": "created by batch script",
        "path": "/v1/banking-unc/template/create",
        "event": "NT000001"
    },
    {
        "id": 389507,
        "desc": "created by batch script",
        "path": "/v1/pricing/fee/query-fee-item",
        "event": "PF000002"
    },
    {
        "id": 389506,
        "desc": "created by batch script",
        "path": "/v1/pricing/fee/create-fee-item",
        "event": "PF000001"
    },
    {
        "id": 389505,
        "desc": "created by batch script",
        "path": "/v1/common/get-option-info",
        "event": "CM000021"
    },
    {
        "id": 389504,
        "desc": "created by batch script",
        "path": "/pr/PI000011",
        "event": "PI000011"
    },
    {
        "id": 389503,
        "desc": "created by batch script",
        "path": "/pr/PI000010",
        "event": "PI000010"
    },
    {
        "id": 389495,
        "desc": "created by batch script",
        "path": "/pr/PI000001",
        "event": "PI000001"
    },
    {
        "id": 389492,
        "desc": "created by batch script",
        "path": "/v1/isaving/history-balance",
        "event": "AC000013"
    },
    {
        "id": 389491,
        "desc": "created by batch script",
        "path": "/v1/wallet/currency-exchange-rate/query",
        "event": "WL500038"
    },
    {
        "id": 389490,
        "desc": "created by batch script",
        "path": "/v1/wallet/common/wallet/config",
        "event": "WL500037"
    },
    {
        "id": 389489,
        "desc": "created by batch script",
        "path": "/v1/wallet/payment/verification",
        "event": "WL5PCPC0"
    },
    {
        "id": 389488,
        "desc": "created by batch script",
        "path": "/v1/wallet/currency/query/list",
        "event": "WL500035"
    },
    {
        "id": 389487,
        "desc": "created by batch script",
        "path": "/v1/wallet/customer/update/kyc",
        "event": "WL500041"
    },
    {
        "id": 389486,
        "desc": "created by batch script",
        "path": "/v1/wallet/calculate-currency-exchange-amount",
        "event": "WL500039"
    },
    {
        "id": 389485,
        "desc": "created by batch script",
        "path": "/v1/wallet/transaction/detail/query",
        "event": "WL500045"
    },
    {
        "id": 389484,
        "desc": "created by batch script",
        "path": "/v1/wallet/payment/sof/validate",
        "event": "WL500044"
    },
    {
        "id": 389483,
        "desc": "created by batch script",
        "path": "/v1/wallet/inter-wallet/create",
        "event": "WL500043"
    },
    {
        "id": 389482,
        "desc": "created by batch script",
        "path": "/v1/wallet/recent/transaction/history/query",
        "event": "WL500014"
    },
    {
        "id": 389481,
        "desc": "created by batch script",
        "path": "/v1/isaving/expiry-deposit-proof:bulk",
        "event": "SV9B0018"
    },
    {
        "id": 389480,
        "desc": "created by batch script",
        "path": "/v1/isaving/revocation-deposit-proof",
        "event": "SV000026"
    },
    {
        "id": 389479,
        "desc": "created by batch script",
        "path": "/v1/isaving/query-deposit-proof-detail",
        "event": "SV000027"
    },
    {
        "id": 389478,
        "desc": "created by batch script",
        "path": "/v1/isaving/query-deposit-proof",
        "event": "SV000025"
    },
    {
        "id": 389477,
        "desc": "created by batch script",
        "path": "/v1/isaving/issuance-deposit-proof",
        "event": "SV000024"
    },
    {
        "id": 389476,
        "desc": "created by batch script",
        "path": "/v1/isaving/query-agreement-iss-depositproof-account",
        "event": "SV000023"
    },
    {
        "id": 389475,
        "desc": "created by batch script",
        "path": "/v1/product/quotastrategylist/query",
        "event": "PD000110"
    },
    {
        "id": 389474,
        "desc": "created by batch script",
        "path": "/v1/product/quotacustomergrade/modify",
        "event": "PD000107"
    },
    {
        "id": 389473,
        "desc": "created by batch script",
        "path": "/v1/product/quotacustomergrade/query",
        "event": "PD000106"
    },
    {
        "id": 389472,
        "desc": "created by batch script",
        "path": "/v1/product/quotacustomergrade/create",
        "event": "PD000105"
    },
    {
        "id": 389471,
        "desc": "created by batch script",
        "path": "/v1/product/repayment_hierarchy/delete",
        "event": "PD000108"
    },
    {
        "id": 389470,
        "desc": "created by batch script",
        "path": "/v1/product/repayment_hierarchy/modify",
        "event": "PD000107"
    },
    {
        "id": 389469,
        "desc": "created by batch script",
        "path": "/v1/product/repayment_hierarchy/query",
        "event": "PD000106"
    },
    {
        "id": 389468,
        "desc": "created by batch script",
        "path": "/v1/product/repayment_hierarchy/create",
        "event": "PD000105"
    },
    {
        "id": 389467,
        "desc": "created by batch script",
        "path": "/v1/product/change/lastrecord",
        "event": "PD000100"
    },
    {
        "id": 389466,
        "desc": "created by batch script",
        "path": "/v1/product/product-examine",
        "event": "PD000096"
    },
    {
        "id": 389465,
        "desc": "created by batch script",
        "path": "/v1/product/limit-control",
        "event": "PD000089"
    },
    {
        "id": 389464,
        "desc": "created by batch script",
        "path": "/v1/product/product-interest-his/query",
        "event": "PD000087"
    },
    {
        "id": 389463,
        "desc": "created by batch script",
        "path": "/v1/product/product-fee-info/query",
        "event": "PD000084"
    },
    {
        "id": 389462,
        "desc": "created by batch script",
        "path": "/v1/product/product-notification-his/query",
        "event": "PD000123"
    },
    {
        "id": 389461,
        "desc": "created by batch script",
        "path": "/v1/product/product-limit-his/query",
        "event": "PD000082"
    },
    {
        "id": 389460,
        "desc": "created by batch script",
        "path": "/v1/product/product-notification-planning/query",
        "event": "PD000123"
    },
    {
        "id": 389459,
        "desc": "created by batch script",
        "path": "/v1/product/product-fee-his/query",
        "event": "PD000080"
    },
    {
        "id": 389458,
        "desc": "created by batch script",
        "path": "/v1/product/product-notification-strategy-list/query",
        "event": "PD000078"
    },
    {
        "id": 389457,
        "desc": "created by batch script",
        "path": "/v1/product/modify-micro-loan-product-simple",
        "event": "PD000070"
    },
    {
        "id": 389456,
        "desc": "created by batch script",
        "path": "/v1/product/query-product-service-pool",
        "event": "PD000069"
    },
    {
        "id": 389455,
        "desc": "created by batch script",
        "path": "/v1/product/create-micro-loan-simple",
        "event": "PD000068"
    },
    {
        "id": 389454,
        "desc": "created by batch script",
        "path": "/v1/product/delete-loan-fee-strategy",
        "event": "PD000058"
    },
    {
        "id": 389453,
        "desc": "created by batch script",
        "path": "/v1/product/delete-loan-interest-strategy",
        "event": "PD000057"
    },
    {
        "id": 389452,
        "desc": "created by batch script",
        "path": "/v1/product/delete-saving-fee-strategy",
        "event": "PD000056"
    },
    {
        "id": 389451,
        "desc": "created by batch script",
        "path": "/v1/product/delete-saving-interest-strategy",
        "event": "PD000055"
    },
    {
        "id": 389450,
        "desc": "created by batch script",
        "path": "/v1/product/modify-category-product",
        "event": "PD000054"
    },
    {
        "id": 389449,
        "desc": "created by batch script",
        "path": "/v1/product/query-micro-loan-full",
        "event": "PD000053"
    },
    {
        "id": 389448,
        "desc": "created by batch script",
        "path": "/v1/product/query-current-deposit-full",
        "event": "PD000052"
    },
    {
        "id": 389447,
        "desc": "created by batch script",
        "path": "/v1/product/modify-loan-fee-strategy",
        "event": "PD000047"
    },
    {
        "id": 389446,
        "desc": "created by batch script",
        "path": "/v1/product/query-loan-fee-strategy",
        "event": "PD000046"
    },
    {
        "id": 389445,
        "desc": "created by batch script",
        "path": "/v1/product/modify-loan-interest-strategy",
        "event": "PD000045"
    },
    {
        "id": 389444,
        "desc": "created by batch script",
        "path": "/v1/product/query-loan-interest-strategy",
        "event": "PD000044"
    },
    {
        "id": 389443,
        "desc": "created by batch script",
        "path": "/v1/product/add-category-product",
        "event": "PD000043"
    },
    {
        "id": 389442,
        "desc": "created by batch script",
        "path": "/v1/product/delete-product-item",
        "event": "PD000021"
    },
    {
        "id": 389441,
        "desc": "created by batch script",
        "path": "/v1/product/modify-product-item",
        "event": "PD000020"
    },
    {
        "id": 389440,
        "desc": "created by batch script",
        "path": "/v1/product/create-product-item",
        "event": "PD000019"
    },
    {
        "id": 389439,
        "desc": "created by batch script",
        "path": "/v1/product/modify-fee-strategy",
        "event": "PD000018"
    },
    {
        "id": 389438,
        "desc": "created by batch script",
        "path": "/v1/product/query-fee-strategy",
        "event": "PD000017"
    },
    {
        "id": 389437,
        "desc": "created by batch script",
        "path": "/v1/product/modify-interest-strategy",
        "event": "PD000016"
    },
    {
        "id": 389436,
        "desc": "created by batch script",
        "path": "/v1/product/query-interest-strategy",
        "event": "PD000015"
    },
    {
        "id": 389435,
        "desc": "created by batch script",
        "path": "/v1/product/delete-micro-loan-product",
        "event": "PD000014"
    },
    {
        "id": 389434,
        "desc": "created by batch script",
        "path": "/v1/product/modify-micro-loan-product",
        "event": "PD000013"
    },
    {
        "id": 389433,
        "desc": "created by batch script",
        "path": "/v1/product/query-micro-loan-product",
        "event": "PD000012"
    },
    {
        "id": 389432,
        "desc": "created by batch script",
        "path": "/v1/product/create-micro-loan-product",
        "event": "PD000011"
    },
    {
        "id": 389431,
        "desc": "created by batch script",
        "path": "/v1/product/delete-current-deposit-product",
        "event": "PD000010"
    },
    {
        "id": 389430,
        "desc": "created by batch script",
        "path": "/v1/product/modify-current-deposit-product",
        "event": "PD000009"
    },
    {
        "id": 389429,
        "desc": "created by batch script",
        "path": "/v1/product/query-current-deposit-product",
        "event": "PD000008"
    },
    {
        "id": 389428,
        "desc": "created by batch script",
        "path": "/v1/product/create-current-deposit-product",
        "event": "PD000007"
    },
    {
        "id": 389427,
        "desc": "created by batch script",
        "path": "/v1/product/query-product-list",
        "event": "PD000006"
    },
    {
        "id": 389426,
        "desc": "created by batch script",
        "path": "/v1/product/query-category-product",
        "event": "PD000005"
    },
    {
        "id": 389425,
        "desc": "created by batch script",
        "path": "/v1/product/delete-category",
        "event": "PD000004"
    },
    {
        "id": 389424,
        "desc": "created by batch script",
        "path": "/v1/product/modify-category",
        "event": "PD000003"
    },
    {
        "id": 389423,
        "desc": "created by batch script",
        "path": "/v1/product/query-category-tree",
        "event": "PD000002"
    },
    {
        "id": 389422,
        "desc": "created by batch script",
        "path": "/v1/product/create-new-category",
        "event": "PD000001"
    },
    {
        "id": 389421,
        "desc": "created by batch script",
        "path": "/v1/customer/CU000088",
        "event": "CU000088"
    },
    {
        "id": 389420,
        "desc": "created by batch script",
        "path": "/v1/customer/CU000084",
        "event": "CU000084"
    },
    {
        "id": 389419,
        "desc": "created by batch script",
        "path": "/v1/customer/CU000083",
        "event": "CU000083"
    },
    {
        "id": 389418,
        "desc": "created by batch script",
        "path": "/v1/customer/CU000070",
        "event": "CU000070"
    },
    {
        "id": 389417,
        "desc": "created by batch script",
        "path": "/v1/customer/CU000045",
        "event": "CU000045"
    },
    {
        "id": 389416,
        "desc": "created by batch script",
        "path": "/v1/customer/CU000061",
        "event": "CU000061"
    },
    {
        "id": 389415,
        "desc": "created by batch script",
        "path": "/v1/customer/customer-list",
        "event": "CU000012"
    },
    {
        "id": 389414,
        "desc": "created by batch script",
        "path": "/v1/customer/customer-count",
        "event": "CU000011"
    },
    {
        "id": 389413,
        "desc": "created by batch script",
        "path": "/v1/customer/update/customer/status",
        "event": "CU000010"
    },
    {
        "id": 389412,
        "desc": "created by batch script",
        "path": "/v1/customer/update/customer/contract/info",
        "event": "CU000009"
    },
    {
        "id": 389411,
        "desc": "created by batch script",
        "path": "/v1/customer/query/customer/contract/info",
        "event": "CU000008"
    },
    {
        "id": 389410,
        "desc": "created by batch script",
        "path": "/v1/customer/contract/count",
        "event": "CU000007"
    },
    {
        "id": 389409,
        "desc": "created by batch script",
        "path": "/v1/customer/query/customer/status",
        "event": "CU000006"
    },
    {
        "id": 389408,
        "desc": "created by batch script",
        "path": "/v1/customer/create/customer/contract",
        "event": "CU000005"
    },
    {
        "id": 389407,
        "desc": "created by batch script",
        "path": "/v1/customer/modify-customer",
        "event": "CU000004"
    },
    {
        "id": 389406,
        "desc": "created by batch script",
        "path": "/v1/customer/query/customer/detail",
        "event": "CU000003"
    },
    {
        "id": 389405,
        "desc": "created by batch script",
        "path": "/v1/wallet/withdraw",
        "event": "WL000004"
    },
    {
        "id": 389404,
        "desc": "created by batch script",
        "path": "/v1/wallet/update-customer",
        "event": "WL000003"
    },
    {
        "id": 389403,
        "desc": "created by batch script",
        "path": "/v1/customer/create-new-customer",
        "event": "CU000002"
    },
    {
        "id": 389402,
        "desc": "created by batch script",
        "path": "/v1/customer/check/customer",
        "event": "CU000001"
    },
    {
        "id": 389401,
        "desc": "created by batch script",
        "path": "/v1/common/system-state-control/query",
        "event": "CM000017"
    },
    {
        "id": 389400,
        "desc": "created by batch script",
        "path": "/v1/common/file-download",
        "event": "CM000007"
    },
    {
        "id": 389399,
        "desc": "created by batch script",
        "path": "/v1/common/file-upload",
        "event": "CM000006"
    },
    {
        "id": 389398,
        "desc": "created by batch script",
        "path": "/upm/v1/account/updateEmail",
        "event": "UPM100051"
    },
    {
        "id": 389397,
        "desc": "created by batch script",
        "path": "/upm/v1/account/sendCode",
        "event": "UPM100050"
    },
    {
        "id": 389396,
        "desc": "created by batch script",
        "path": "/upm/v1/account/resetPassword",
        "event": "UPM100052"
    },
    {
        "id": 389395,
        "desc": "created by batch script",
        "path": "/upm/v1/code/verify",
        "event": "UPM100049"
    },
    {
        "id": 389394,
        "desc": "created by batch script",
        "path": "/upm/v1/code/send",
        "event": "UPM100048"
    },
    {
        "id": 389393,
        "desc": "created by batch script",
        "path": "/upm/v1/link/send",
        "event": "UPM100054"
    },
    {
        "id": 389392,
        "desc": "created by batch script",
        "path": "/upm/v1/link/check",
        "event": "UPM100053"
    },
    {
        "id": 389391,
        "desc": "created by batch script",
        "path": "/upm/v1/account/register",
        "event": "UPM100047"
    },
    {
        "id": 389390,
        "desc": "created by batch script",
        "path": "/upm/v1/role/detail",
        "event": "UPM100019"
    },
    {
        "id": 389389,
        "desc": "created by batch script",
        "path": "/upm/v1/role/update",
        "event": "UPM100018"
    },
    {
        "id": 389388,
        "desc": "created by batch script",
        "path": "/upm/v1/role/list",
        "event": "UPM100016"
    },
    {
        "id": 389387,
        "desc": "created by batch script",
        "path": "/upm/v1/role/delete",
        "event": "UPM100020"
    },
    {
        "id": 389386,
        "desc": "created by batch script",
        "path": "/upm/v1/role/create",
        "event": "UPM100017"
    },
    {
        "id": 389385,
        "desc": "created by batch script",
        "path": "/upm/v1/group/removeUser",
        "event": "UPM100056"
    },
    {
        "id": 389384,
        "desc": "created by batch script",
        "path": "/upm/v1/group/updateUser",
        "event": "UPM100058"
    },
    {
        "id": 389383,
        "desc": "created by batch script",
        "path": "/upm/v1/group/addUser",
        "event": "UPM100055"
    },
    {
        "id": 389382,
        "desc": "created by batch script",
        "path": "/upm/v1/group/listUser",
        "event": "UPM100057"
    },
    {
        "id": 389381,
        "desc": "created by batch script",
        "path": "/upm/v1/group/list",
        "event": "UPM100001"
    },
    {
        "id": 389380,
        "desc": "created by batch script",
        "path": "/upm/v1/group/delete",
        "event": "UPM100004"
    },
    {
        "id": 389379,
        "desc": "created by batch script",
        "path": "/upm/v1/group/update",
        "event": "UPM100003"
    },
    {
        "id": 389378,
        "desc": "created by batch script",
        "path": "/upm/v1/group/create",
        "event": "UPM100002"
    },
    {
        "id": 389377,
        "desc": "created by batch script",
        "path": "/upm/v1/account/unlock",
        "event": "UPM100012"
    },
    {
        "id": 389376,
        "desc": "created by batch script",
        "path": "/upm/v1/account/enable",
        "event": "UPM100031"
    },
    {
        "id": 389375,
        "desc": "created by batch script",
        "path": "/upm/v1/account/disable",
        "event": "UPM100011"
    },
    {
        "id": 389374,
        "desc": "created by batch script",
        "path": "/upm/v1/account/list",
        "event": "UPM100005"
    },
    {
        "id": 389373,
        "desc": "created by batch script",
        "path": "/upm/v1/account/detail",
        "event": "UPM100008"
    },
    {
        "id": 389372,
        "desc": "created by batch script",
        "path": "/upm/v1/account/delete",
        "event": "UPM100010"
    },
    {
        "id": 389371,
        "desc": "created by batch script",
        "path": "/upm/v1/account/update",
        "event": "UPM100009"
    },
    {
        "id": 389370,
        "desc": "created by batch script",
        "path": "/upm/v1/account/create",
        "event": "UPM100007"
    },
    {
        "id": 389369,
        "desc": "created by batch script",
        "path": "/upm/v1/account/changePasswd",
        "event": "UPM100014"
    },
    {
        "id": 389368,
        "desc": "created by batch script",
        "path": "/upm/v1/account/editProfile",
        "event": "UPM100006"
    },
    {
        "id": 389367,
        "desc": "created by batch script",
        "path": "/upm/v1/account/updateGoogleAuth",
        "event": "UPM100040"
    },
    {
        "id": 389366,
        "desc": "created by batch script",
        "path": "/upm/v1/tenant/unlock",
        "event": "UPM100041"
    },
    {
        "id": 389365,
        "desc": "created by batch script",
        "path": "/upm/v1/tenant/list",
        "event": "UPM100021"
    },
    {
        "id": 389364,
        "desc": "created by batch script",
        "path": "/upm/v1/tenant/enable",
        "event": "UPM100025"
    },
    {
        "id": 389363,
        "desc": "created by batch script",
        "path": "/upm/v1/tenant/disable",
        "event": "UPM100024"
    },
    {
        "id": 389362,
        "desc": "created by batch script",
        "path": "/upm/v1/tenant/delete",
        "event": "UPM100026"
    },
    {
        "id": 389361,
        "desc": "created by batch script",
        "path": "/upm/v1/tenant/update",
        "event": "UPM100023"
    },
    {
        "id": 389360,
        "desc": "created by batch script",
        "path": "/upm/v1/tenant/create",
        "event": "UPM100022"
    },
    {
        "id": 389359,
        "desc": "created by batch script",
        "path": "/upm/v1/account/setPasswd",
        "event": "UPM100015"
    },
    {
        "id": 389358,
        "desc": "created by batch script",
        "path": "/upm/v1/logout",
        "event": "UPM100030"
    },
    {
        "id": 389357,
        "desc": "created by batch script",
        "path": "/upm/v1/GetOperationPermission",
        "event": "UPM100013"
    },
    {
        "id": 389356,
        "desc": "created by batch script",
        "path": "/upm/v1/account/GetRsaPublicKey",
        "event": "UPM100029"
    },
    {
        "id": 389355,
        "desc": "created by batch script",
        "path": "/upm/v1/menu/get",
        "event": "UPM100043"
    },
    {
        "id": 389354,
        "desc": "created by batch script",
        "path": "/upm/v1/account/GoogleAuth",
        "event": "UPMGoogleAuth"
    },
    {
        "id": 389353,
        "desc": "created by batch script",
        "path": "/upm/v1/account/check",
        "event": "UPM100059"
    },
    {
        "id": 389352,
        "desc": "created by batch script",
        "path": "/upm/v1/newLogin",
        "event": "UPMLogin"
    },
    {
        "id": 389350,
        "desc": "created by batch script",
        "path": "/mu/v1/shortpath/generate",
        "event": "SHORTpathBSS"
    },
    {
        "id": 389349,
        "desc": "created by batch script",
        "path": "/v1/customer/query-customer-information",
        "event": "CU000003"
    },
    {
        "id": 389348,
        "desc": "created by batch script",
        "path": "/v1/customer/create-customer",
        "event": "CU000002"
    },
    {
        "id": 389347,
        "desc": "created by batch script",
        "path": "/v1/customer/check-newcustomer",
        "event": "CU000001"
    },
    {
        "id": 389345,
        "desc": "created by batch script",
        "path": "/ac/sac1020003",
        "event": "AC120003"
    },
    {
        "id": 389344,
        "desc": "created by batch script",
        "path": "/ac/sac1020002",
        "event": "AC120002"
    },
    {
        "id": 389343,
        "desc": "created by batch script",
        "path": "/v1/isaving/cancellation-demand-deposit",
        "event": "SV000021"
    },
    {
        "id": 389342,
        "desc": "created by batch script",
        "path": "/v1/isaving/query-agreement-information-cancellation",
        "event": "SV000020"
    },
    {
        "id": 389341,
        "desc": "created by batch script",
        "path": "/v1/wallet/get-public-pin-key",
        "event": "SV000008"
    },
    {
        "id": 389340,
        "desc": "created by batch script",
        "path": "/v1/isaving/query-publickey",
        "event": "SV000008"
    },
    {
        "id": 389339,
        "desc": "created by batch script",
        "path": "/v1/isaving/unfreeze-account",
        "event": "SV000007"
    },
    {
        "id": 389338,
        "desc": "created by batch script",
        "path": "/v1/isaving/query-freeze-information-deduction",
        "event": "SV000019"
    },
    {
        "id": 389337,
        "desc": "created by batch script",
        "path": "/v1/isaving/account-deduction",
        "event": "SV000018"
    },
    {
        "id": 389336,
        "desc": "created by batch script",
        "path": "/v1/isaving/continued-freezing",
        "event": "SV000017"
    },
    {
        "id": 389335,
        "desc": "created by batch script",
        "path": "/v1/isaving/query-transaction-detail",
        "event": "SV000016"
    },
    {
        "id": 389334,
        "desc": "created by batch script",
        "path": "/v1/isaving/accounting-correction",
        "event": "SV000015"
    },
    {
        "id": 389333,
        "desc": "created by batch script",
        "path": "/v1/isaving/unsuspend",
        "event": "SV000014"
    },
    {
        "id": 389332,
        "desc": "created by batch script",
        "path": "/v1/isaving/suspend",
        "event": "SV000013"
    },
    {
        "id": 389331,
        "desc": "created by batch script",
        "path": "/v1/isaving/query-freeze-unfreezededuction-flow",
        "event": "SV000012"
    },
    {
        "id": 389330,
        "desc": "created by batch script",
        "path": "/v1/isaving/update-password",
        "event": "SV000011"
    },
    {
        "id": 389329,
        "desc": "created by batch script",
        "path": "/v1/isaving/query-freeze-informatio",
        "event": "SV000009"
    },
    {
        "id": 389328,
        "desc": "created by batch script",
        "path": "/v1/isaving/setting-debcrd-status",
        "event": "SV000010"
    },
    {
        "id": 389327,
        "desc": "created by batch script",
        "path": "/v1/cm/CM090003",
        "event": "CM090003"
    },
    {
        "id": 389326,
        "desc": "created by batch script",
        "path": "/v1/cm/CM090002",
        "event": "CM090002"
    },
    {
        "id": 389325,
        "desc": "created by batch script",
        "path": "/v1/isaving/dormant-account/activate",
        "event": "SV000022"
    },
    {
        "id": 389324,
        "desc": "created by batch script",
        "path": "/v1/isaving/freeze-account",
        "event": "SV000006"
    },
    {
        "id": 389323,
        "desc": "created by batch script",
        "path": "/v1/isaving/deposit-transfer",
        "event": "SV000005"
    },
    {
        "id": 389322,
        "desc": "created by batch script",
        "path": "/v1/isaving/agreement/detail",
        "event": "SV000030"
    },
    {
        "id": 389321,
        "desc": "created by batch script",
        "path": "/v1/isaving/agreement/list",
        "event": "SV000029"
    },
    {
        "id": 389320,
        "desc": "created by batch script",
        "path": "/v1/isaving/continue-frozen/info",
        "event": "SV000037"
    },
    {
        "id": 389319,
        "desc": "created by batch script",
        "path": "/v1/isaving/deduction/info",
        "event": "SV000036"
    },
    {
        "id": 389318,
        "desc": "created by batch script",
        "path": "/v1/isaving/unfrozen/info",
        "event": "SV000035"
    },
    {
        "id": 389317,
        "desc": "created by batch script",
        "path": "/v1/isaving/frozen/relation/list",
        "event": "SV000034"
    },
    {
        "id": 389316,
        "desc": "created by batch script",
        "path": "/v1/isaving/frozen/info",
        "event": "SV000033"
    },
    {
        "id": 389315,
        "desc": "created by batch script",
        "path": "/v1/isaving/deduction/list",
        "event": "SV000032"
    },
    {
        "id": 389314,
        "desc": "created by batch script",
        "path": "/v1/isaving/frozen/list",
        "event": "SV000031"
    },
    {
        "id": 389313,
        "desc": "created by batch script",
        "path": "/v1/sv/ssv9b00016",
        "event": "SV9B0016"
    },
    {
        "id": 389312,
        "desc": "created by batch script",
        "path": "/v1/sv/ssv9b00015",
        "event": "SV9B0015"
    },
    {
        "id": 389311,
        "desc": "created by batch script",
        "path": "/v1/sv/ssv1000022",
        "event": "SV100022"
    },
    {
        "id": 389310,
        "desc": "created by batch script",
        "path": "/v1/sv/ssv1000021",
        "event": "SV100021"
    },
    {
        "id": 389309,
        "desc": "created by batch script",
        "path": "/v1/isaving/mnaccountmsg",
        "event": "SV000004"
    },
    {
        "id": 389308,
        "desc": "created by batch script",
        "path": "/v1/isaving/sltaccountmsg",
        "event": "SV000003"
    },
    {
        "id": 389307,
        "desc": "created by batch script",
        "path": "/v1/isaving/trial/interest",
        "event": "SV000042"
    },
    {
        "id": 389306,
        "desc": "created by batch script",
        "path": "/v1/templatelist/query",
        "event": "PD000110"
    },
    {
        "id": 389305,
        "desc": "created by batch script",
        "path": "/v1/pricing/fee/fee-trial",
        "event": "SV000070"
    },
    {
        "id": 389304,
        "desc": "created by batch script",
        "path": "/v1/isaving/agreement/queryaddtion",
        "event": "SV000043"
    },
    {
        "id": 389303,
        "desc": "created by batch script",
        "path": "/v1/isaving/agreement/ratetrial",
        "event": "SV000044"
    },
    {
        "id": 389302,
        "desc": "created by batch script",
        "path": "/v1/isaving/createpersonaccount",
        "event": "SV000001"
    },
    {
        "id": 389301,
        "desc": "created by batch script",
        "path": "/v1/isaving/sltprdlist",
        "event": "SV000002"
    },
    {
        "id": 389300,
        "desc": "created by batch script",
        "path": "/in/sin9000010",
        "event": "IN900010"
    },
    {
        "id": 389291,
        "desc": "created by batch script",
        "path": "/in/sin9000001",
        "event": "IN900001"
    },
    {
        "id": 389290,
        "desc": "created by batch script",
        "path": "/in/sin1000006",
        "event": "IN100006"
    },
    {
        "id": 389289,
        "desc": "created by batch script",
        "path": "/in/sin1000005",
        "event": "IN100005"
    },
    {
        "id": 389288,
        "desc": "created by batch script",
        "path": "/in/sin1000004",
        "event": "IN100004"
    },
    {
        "id": 389287,
        "desc": "created by batch script",
        "path": "/in/sin1000003",
        "event": "IN100003"
    },
    {
        "id": 389286,
        "desc": "created by batch script",
        "path": "/in/sin1000002",
        "event": "IN100002"
    },
    {
        "id": 389280,
        "desc": "created by batch script",
        "path": "/in/sin0000001",
        "event": "IN000001"
    },
    {
        "id": 389277,
        "desc": "created by batch script",
        "path": "/at/sat0000001",
        "event": "AT000001"
    },
    {
        "id": 389276,
        "desc": "created by batch script",
        "path": "/v1/wallet/skm/UpdateSecret",
        "event": "UpdateSecret"
    },
    {
        "id": 389275,
        "desc": "created by batch script",
        "path": "/v1/wallet/skm/GetSecret",
        "event": "GetSecret"
    },
    {
        "id": 389274,
        "desc": "created by batch script",
        "path": "/v1/wallet/skm/AddSecret",
        "event": "AddSecret"
    },
    {
        "id": 389273,
        "desc": "created by batch script",
        "path": "/v1/wallet/withdraw/confirm",
        "event": "WL500026"
    },
    {
        "id": 389272,
        "desc": "created by batch script",
        "path": "/v1/wallet/pricing/exchange-rate/user",
        "event": "WL100003"
    },
    {
        "id": 389271,
        "desc": "created by batch script",
        "path": "/v1/wallet/withdrawal",
        "event": "WL000008"
    },
    {
        "id": 389270,
        "desc": "created by batch script",
        "path": "/v1/wallet/troy/reap-webhook/subscribe",
        "event": "WL000042"
    },
    {
        "id": 389269,
        "desc": "created by batch script",
        "path": "/v1/wallet/reap/query/card",
        "event": "WL300027"
    },
    {
        "id": 389268,
        "desc": "created by batch script",
        "path": "/v1/wallet/card/detail",
        "event": "WL500070"
    },
    {
        "id": 389267,
        "desc": "created by batch script",
        "path": "/v1/wallet/card/pan-detail",
        "event": "WL500066"
    },
    {
        "id": 389266,
        "desc": "created by batch script",
        "path": "/v1/wallet/card/create",
        "event": "WL500065"
    },
    {
        "id": 389265,
        "desc": "created by batch script",
        "path": "/v1/wallet/topup",
        "event": "WL000002"
    },
    {
        "id": 389264,
        "desc": "created by batch script",
        "path": "/v1/wallet/partner/ratio/query",
        "event": "DLPN0QUY022"
    },
    {
        "id": 389263,
        "desc": "created by batch script",
        "path": "/v1/wallet/pointaccount/query",
        "event": "DLPT0QUY005"
    },
    {
        "id": 389262,
        "desc": "created by batch script",
        "path": "/v1/wallet/myaotpassidtype",
        "event": "WL0CTPQ1"
    },
    {
        "id": 389261,
        "desc": "created by batch script",
        "path": "/v1/wallet/cm0gtpm2",
        "event": "CM0GTPM2"
    },
    {
        "id": 389260,
        "desc": "created by batch script",
        "path": "/v1/wallet/batch/sendemailbss",
        "event": "WL0SOEM0"
    },
    {
        "id": 389259,
        "desc": "created by batch script",
        "path": "/v1/wallet/das/querycontinfo",
        "event": "DAWLRCI0"
    },
    {
        "id": 389258,
        "desc": "created by batch script",
        "path": "/v1/wallet/payment/result/query",
        "event": "WL0PRQR0"
    },
    {
        "id": 389257,
        "desc": "created by batch script",
        "path": "/v1/wallet/payment/result/notifybywps",
        "event": "WL0PRNW0"
    },
    {
        "id": 389256,
        "desc": "created by batch script",
        "path": "/v1/wallet/payment/result/notifybyfastpay",
        "event": "WL1PRNF0"
    },
    {
        "id": 389255,
        "desc": "created by batch script",
        "path": "/v1/wallet/payment/request",
        "event": "WL0PMRQ0"
    },
    {
        "id": 389254,
        "desc": "created by batch script",
        "path": "/v1/wallet/payment/order/ref/request",
        "event": "WL0PORR0"
    },
    {
        "id": 389253,
        "desc": "created by batch script",
        "path": "/v1/wallet/payment/order/query",
        "event": "WL0POQR0"
    },
    {
        "id": 389252,
        "desc": "created by batch script",
        "path": "/v1/wallet/payment/confirm",
        "event": "WL0PMCF0"
    },
    {
        "id": 389251,
        "desc": "created by batch script",
        "path": "/v1/wallet/payment/confirm/precheck",
        "event": "WL0PCPC1"
    },
    {
        "id": 389250,
        "desc": "created by batch script",
        "path": "/v1/wallet/payment/confirm/prececk",
        "event": "WL0PCPC0"
    },
    {
        "id": 389249,
        "desc": "created by batch script",
        "path": "/v1/wallet/addpaycardfb",
        "event": "WL0ADCF0"
    },
    {
        "id": 389248,
        "desc": "created by batch script",
        "path": "/v1/wallet/inquirypaycardfb",
        "event": "WL0PMQR0"
    },
    {
        "id": 389247,
        "desc": "created by batch script",
        "path": "/v1/wallet/deletepaycardfb",
        "event": "WL0PMDL0"
    },
    {
        "id": 389246,
        "desc": "created by batch script",
        "path": "/v1/wallet/bindwcresultnotify",
        "event": "WL0BWRSN"
    },
    {
        "id": 389245,
        "desc": "created by batch script",
        "path": "/v1/wallet/payment/wpstransreverse",
        "event": "WL1APIW3"
    },
    {
        "id": 389244,
        "desc": "created by batch script",
        "path": "/v1/wallet/blockchain/tokens/get",
        "event": "WL300026"
    },
    {
        "id": 389243,
        "desc": "created by batch script",
        "path": "/v1/wallet/awl3000017",
        "event": "WL300017"
    },
    {
        "id": 389242,
        "desc": "created by batch script",
        "path": "/v1/wallet/card/delete",
        "event": "WL500071"
    },
    {
        "id": 389241,
        "desc": "created by batch script",
        "path": "/v1/wallet/card/freeze",
        "event": "WL500062"
    },
    {
        "id": 389240,
        "desc": "created by batch script",
        "path": "/v1/wallet/awl3000025",
        "event": "WL300025"
    },
    {
        "id": 389239,
        "desc": "created by batch script",
        "path": "/v1/wallet/awl3000013",
        "event": "WL300013"
    },
    {
        "id": 389238,
        "desc": "created by batch script",
        "path": "/v1/api/wallet/force-update",
        "event": "WL500072"
    },
    {
        "id": 389237,
        "desc": "created by batch script",
        "path": "/v1/wallet/payment/wpstransactionquery",
        "event": "WL0APIW0"
    },
    {
        "id": 389236,
        "desc": "created by batch script",
        "path": "/v1/wallet/querypaymethod",
        "event": "WL0PBRQ0"
    },
    {
        "id": 389235,
        "desc": "created by batch script",
        "path": "/v1/wallet/payment/order/status/update",
        "event": "WL0POSU0"
    },
    {
        "id": 389234,
        "desc": "created by batch script",
        "path": "/v1/wallet/homepageinquiry",
        "event": "WL0HOME1"
    },
    {
        "id": 389233,
        "desc": "created by batch script",
        "path": "/v1/wallet/otpvalidate",
        "event": "CM0OTPV1"
    },
    {
        "id": 389232,
        "desc": "created by batch script",
        "path": "/v1/wallet/otprequest",
        "event": "WL0OTPA0"
    },
    {
        "id": 389231,
        "desc": "created by batch script",
        "path": "/v1/wallet/myaotpassinformation",
        "event": "WL0CTPQ0"
    },
    {
        "id": 389230,
        "desc": "created by batch script",
        "path": "/v1/wallet/setpin",
        "event": "WL0SPIN0"
    },
    {
        "id": 389229,
        "desc": "created by batch script",
        "path": "/v1/wallet/resetpin",
        "event": "WL0RPIN0"
    },
    {
        "id": 389228,
        "desc": "created by batch script",
        "path": "/v1/wallet/verifypin",
        "event": "WL0VPIN0"
    },
    {
        "id": 389227,
        "desc": "created by batch script",
        "path": "/v1/wallet/amllistandkycscoreverify",
        "event": "WL0ALKS0"
    },
    {
        "id": 389226,
        "desc": "created by batch script",
        "path": "/v1/wallet/loginrequest",
        "event": "WL0AOTQ0"
    },
    {
        "id": 389225,
        "desc": "created by batch script",
        "path": "/v1/wallet/login",
        "event": "WL0LOGN1"
    },
    {
        "id": 389224,
        "desc": "created by batch script",
        "path": "/v1/file",
        "event": ""
    },
    {
        "id": 389223,
        "desc": "created by batch script",
        "path": "/v1/wallet/upload",
        "event": "WL000039"
    },
    {
        "id": 389222,
        "desc": "created by batch script",
        "path": "/v1/wallet/register-customer",
        "event": "WL0RGST1"
    },
    {
        "id": 389221,
        "desc": "created by batch script",
        "path": "/v1/wallet/wps/reverse",
        "event": "WL0APIW3"
    },
    {
        "id": 389220,
        "desc": "created by batch script",
        "path": "/v1/wallet/wps/close",
        "event": "WL0APIW2"
    },
    {
        "id": 389219,
        "desc": "created by batch script",
        "path": "/v1/wallet/wps/create",
        "event": "WL0APIW1"
    },
    {
        "id": 389218,
        "desc": "created by batch script",
        "path": "/open-banking/admin/v1/metrics",
        "event": "OB0O0001"
    }
]

# List of API names provided by the user
api_names_new = [
    "Sign up", "Login", "Verify pin", "Pin reset", "Log out", "Edit app user detail",
    "User consent signature", "OTP send", "OTP verify", "Force log out", "Login by biometric",
    "Check user", "Query user‘s wallet id", "Get wallet consent", "Create wallet account",
    "Create sub-wallet account", "Inter wallet account consent inquiry", "Create inter wallet account",
    "Wallet account balance query", "Update source of fund card", "Payment card freeze",
    "Payment card binding", "Payment card detail", "Payment card list", "Payment card detail",
    "Payment card delete", "Query source of fund card list", "List account balance", "Wallet withdraw",
    "Wallet top up", "Query default account", "Query currency exchange rate", "Sub-wallet top up",
    "Sub-wallet withdraw", "Create scan to pay order", "Create payment order", "Payment Confirmation",
    "Wallet account verification", "Confirm transfer transaction", "Offline payment",
    "Verifier confirm payment", "Wallet sync payment transaction", "Wallet settlement",
    "Verifier settlement", "Query wallet transaction history list", "View transaction detail",
    "Query Point payment exchange", "Point transfer confirm", "Add membership card account",
    "Query user exchange rate", "Query membership and partner", "Partner transfer getlist",
    "Query customer point account detail information", "KYC access token query",
    "Wallet app new version notification", "2fa update", "2fa query", "2fa Verification",
    "Query wallet transaction limit", "Update wallet transaction limit", "Query list of wallet limit parameter",
    "Query pin limit rule"
]

# Mapping API names to actual JSON data and preparing the CSV output
output_rows_new = []
for api_name in api_names_new:
    # Attempt to find a corresponding API from the JSON data by name similarity or identifier matching
    for api in api_data_new:
        if api_name.lower() in api['desc'].lower() or api_name.lower().replace(" ", "-") in api['path'].lower():
            # Found a match, adding to the CSV output
            output_rows_new.append([api_name, api['event'], api['path'], api['id']])
            break

# Writing the output to a CSV file
output_file_new = 'api_mapping_output.csv'

with open(output_file_new, mode='w', newline='') as file:
    writer = csv.writer(file)
    writer.writerow(["name", "event", "path", "id"])  # Writing the CSV header
    writer.writerows(output_rows_new)

output_file_new  # Return the path to the generated CSV file