import fitz
import json
import sys

# https://pymupdf.readthedocs.io/en/latest/recipes-text.html

fname = sys.argv[1]
# fname = "doc.pdf"
doc = fitz.open(fname)
for page in doc: # iterate the document pages
  # text = page.get_text() # get plain text encoded as UTF-8
  # print(text)

  tables = page.find_tables()
  for table in tables:
    text = table.extract()
    json_str = json.dumps(text)
    print(json_str)

    # print(text)
    # for row in text:
    #   for cell in row:
    #     print(cell)
  # df = tables[0].to_pandas()
  # df.to_excel('table.xlsx', index=False)